{"Version": 1, "WorkspaceRootPath": "D:\\AIProjectTest\\GamalCompanyApp\\GamalComapnyApp\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{B198C109-61F2-411B-B7D0-7C58A2925EA2}|GamalComapany.Service\\GamalComapany.Service.csproj|d:\\aiprojecttest\\gamalcompanyapp\\gamalcomapnyapp\\gamalcomapany.service\\mapping\\mappingprofile.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B198C109-61F2-411B-B7D0-7C58A2925EA2}|GamalComapany.Service\\GamalComapany.Service.csproj|solutionrelative:gamalcomapany.service\\mapping\\mappingprofile.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B198C109-61F2-411B-B7D0-7C58A2925EA2}|GamalComapany.Service\\GamalComapany.Service.csproj|d:\\aiprojecttest\\gamalcompanyapp\\gamalcomapnyapp\\gamalcomapany.service\\dtos\\inventorydto\\inventorydto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B198C109-61F2-411B-B7D0-7C58A2925EA2}|GamalComapany.Service\\GamalComapany.Service.csproj|solutionrelative:gamalcomapany.service\\dtos\\inventorydto\\inventorydto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 1, "Children": [{"$type": "Document", "DocumentIndex": 1, "Title": "InventoryDto.cs", "DocumentMoniker": "D:\\AIProjectTest\\GamalCompanyApp\\GamalComapnyApp\\GamalComapany.Service\\Dtos\\InventoryDto\\InventoryDto.cs", "RelativeDocumentMoniker": "GamalComapany.Service\\Dtos\\InventoryDto\\InventoryDto.cs", "ToolTip": "D:\\AIProjectTest\\GamalCompanyApp\\GamalComapnyApp\\GamalComapany.Service\\Dtos\\InventoryDto\\InventoryDto.cs", "RelativeToolTip": "GamalComapany.Service\\Dtos\\InventoryDto\\InventoryDto.cs", "ViewState": "AgIAAFwAAAAAAAAAAAAUwGMAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T08:04:28.035Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "MappingProfile.cs", "DocumentMoniker": "D:\\AIProjectTest\\GamalCompanyApp\\GamalComapnyApp\\GamalComapany.Service\\Mapping\\MappingProfile.cs", "RelativeDocumentMoniker": "GamalComapany.Service\\Mapping\\MappingProfile.cs", "ToolTip": "D:\\AIProjectTest\\GamalCompanyApp\\GamalComapnyApp\\GamalComapany.Service\\Mapping\\MappingProfile.cs*", "RelativeToolTip": "GamalComapany.Service\\Mapping\\MappingProfile.cs*", "ViewState": "AgIAABkAAAAAAAAAAAApwCUAAAA0AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T08:03:56.755Z", "EditorCaption": ""}]}]}]}