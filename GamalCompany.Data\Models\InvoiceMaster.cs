﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GamalCompany.Data.Models
{
    public class InvoiceMaster : BaseEntity
    {
        public int InvoiceTypeId { get; set; }
        public string? InvoiceNumber { get; set; }
        public DateTime InvoiceDate { get; set; }
        public int? CustomerId { get; set; }
        public int? DepartmentId { get; set; }

        [Column(TypeName = "decimal(18,4)")]
        public decimal? SubTotal { get; set; }
        [Column(TypeName = "decimal(18,4)")]
        public decimal? DiscountAmount { get; set; }
        [Column(TypeName = "decimal(18,2)")]
        public decimal? DiscountPercentage { get; set; }
        [Column(TypeName = "decimal(18,4)")]
        public decimal? TaxAmount { get; set; }
        [Column(TypeName = "decimal(18,2)")]
        public decimal? TaxPercentage { get; set; }
        [Column(TypeName = "decimal(18,4)")]
        public decimal? TotalAmount { get; set; }
        [Column(TypeName = "decimal(18,4)")]
        public decimal? PaidAmount { get; set; }
        [Column(TypeName = "decimal(18,4)")]
        public decimal? RemainingAmount { get; set; }
        public string? PaymentStatus { get; set; }
        public string? Notes { get; set; }

        [ForeignKey(nameof(InvoiceTypeId))]
        public virtual MainAction MainAction { get; set; } = null!;

        [ForeignKey(nameof(CustomerId))]
        public virtual SupplierCustomer SupplierCustomer { get; set; } = null!;
        
        [ForeignKey(nameof(DepartmentId))]
        public virtual CompanyDepartment CompanyDepartment { get; set; } = null!;

        
        public virtual ICollection<InvoiceDetail> InvoiceDetails { get; set; } = new List<InvoiceDetail>();
    }
}
