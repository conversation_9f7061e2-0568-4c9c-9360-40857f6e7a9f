﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GamalCompany.Data.Models
{
    public class SupplierCustomer : BaseName
    {
        public int? VanderTypeId { get; set; }

        [ForeignKey(nameof(VanderTypeId))]
        public virtual VanderType? VanderType { get; set; }
    }
}
