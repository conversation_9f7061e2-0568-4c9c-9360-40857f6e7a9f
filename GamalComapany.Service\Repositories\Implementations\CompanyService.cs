﻿using GamalComapany.Service.Dtos;
using GamalComapany.Service.Dtos.CompanyDto;
using GamalComapany.Service.Repositories.Interfaces;
using GamalCompany.Data.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace GamalComapany.Service.Repositories.Implementations
{
    public class CompanyService : ResponseHandler, ICompanyRepository
    {
        private IUnitOfWorkOfService _work;

        public CompanyService(IUnitOfWorkOfService work)
        {
            _work = work;
        }

        public async Task<ApiResponse<Company>> AddCompany(CreateCompanyDto company)
        {

            var data = await _work.Companies.GetTableNoTracking().FirstOrDefaultAsync();
            if (data != null) return NotFound<Company>("يوجد شركة بالفعل");
            else
            {
                if(company == null) return NotFound<Company>("اضف البيانات اولا");

                var Compa = new Company
                {
                    Id = 0,
                    Symbol = company.Symbol,
                    NameEn = company.NameEn,
                    Description = company.Description,
                    Code = company.Code,
                    LogePath = company.LogePath
                };

                await _work.Companies.AddAsync(Compa);
                return Success(Compa);
            }
              
        }

        public async Task<ApiResponse<CompanyDepartment>> AddDepartment(CearteDepartmentDto depatment)
        {
           if(depatment == null) return NotFound<CompanyDepartment>("البيانات فارغة");
            bool exist =await _work.CompanyDepartments.ExistsAsync(a => a.NameEn == depatment.NameEn);
            if (exist) return NotFound<CompanyDepartment>("هذا القسم موجود من قبل");
            var newEnitiy = new CompanyDepartment()
            {
                NameEn = depatment.NameEn,
                Description = depatment.Description,
                Code = depatment.Code,
                Symbol = depatment.Symbol,
                Id = 0,
                CreatedBy = 1
                
            };

            await _work.CompanyDepartments.AddAsync(newEnitiy);
            return Success(newEnitiy);
        }

        public async Task<ApiResponse<Company>> GetCompany()
        {
            var data = await _work.Companies.GetTableNoTracking().FirstOrDefaultAsync();
            if (data == null) return NotFound<Company>("لا يوجد بيانات للشركة");
            return Success(data);
        }

        public async Task<ApiResponse<List<CompanyDepartmentDto>>> GetDepartment()
        {
            var data = await _work.CompanyDepartments.GetTableNoTracking().ToListAsync();
            if (data == null || data.Count == 0) return NotFound<List<CompanyDepartmentDto>>();
            var entity = data.Select(d => new CompanyDepartmentDto
            {
               Id=d.Id, NameEn = d.NameEn, Description = d.Description, Code = d.Code,  Symbol = d.Symbol
      
            }).ToList();
            return Success(entity); 
        }

        public async Task<ApiResponse<Company>> UpdateCompany(UpdateCompanyDto company)
        {
            var data = await _work.Companies.GetTableNoTracking().FirstOrDefaultAsync();
            if (data == null) return NotFound<Company>("لا يوجد بيانات للشركة");

            data.NameEn = company.NameEn;
            data.Description = company.Description;
            data.Code = company.Code;
            data.Symbol = company.Symbol;
            data.LogePath = company.LogePath;
            data.UpdatedBy = 1;
            data.UpdatedAt = DateTime.Now;
            await _work.Companies.UpdateAsync(data);
            return Success(data);
        }

        public async Task<ApiResponse<CompanyDepartment>> UpdateDepartment(UpdateDepartmentDto depatmentcompany)
        {
            var data = await _work.CompanyDepartments.GetByIdAsync(depatmentcompany.Id);
            if (data == null) return NotFound<CompanyDepartment>("لا يوجد هذا القسم");

            data.NameEn = depatmentcompany.NameEn;
            data.Description = depatmentcompany.Description;
            data.Code = depatmentcompany.Code;
            data.Symbol = depatmentcompany.Symbol;
            data.UpdatedBy = 1;
            data.UpdatedAt = DateTime.Now;
            await _work.CompanyDepartments.UpdateAsync(data);
            return Success(data);
        }
    }
}
