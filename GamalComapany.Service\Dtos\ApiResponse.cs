﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;

namespace GamalComapany.Service.Dtos
{
    public class ApiResponse<T>
    {

        public HttpStatusCode StatusCode { get; set; }
        public object Meta { get; set; } = new object();
        public bool Succeeded { get; set; }
        public string Message { get; set; } = string.Empty;
        public List<string> Errors { get; set; } = [];
        public T Data { get; set; } = default!;

        public ApiResponse()
        {
            Meta = new object();
            Message = string.Empty;
        }

        public ApiResponse(T data, string message, object? meta = null)
        {
            Succeeded = true;
            Message = message ?? "";
            Data = data;
            Meta = meta ?? new object();
        }
        public ApiResponse(string message, object? meta = null)
        {
            Succeeded = false;
            Message = message;
            Meta = meta ?? new object();
            Data = default!;

        }
        public ApiResponse(string message, bool succeeded, object? meta = null)
        {
            Succeeded = succeeded;
            Message = message;
            Meta = meta ?? new object();
            Data = default!;
        }


    }
}