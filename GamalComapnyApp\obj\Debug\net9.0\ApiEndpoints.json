[{"ContainingType": "GamalComapnyApp.API.Controllers.CampanyController", "Method": "GetCompany", "RelativePath": "api/Campany", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.CampanyController", "Method": "AddCompany", "RelativePath": "api/Campany", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "company", "Type": "GamalComapany.Service.Dtos.CompanyDto.CreateCompanyDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.CampanyController", "Method": "UpdateCompany", "RelativePath": "api/Campany", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "company", "Type": "GamalComapany.Service.Dtos.CompanyDto.UpdateCompanyDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.CampanyController", "Method": "AddDepartment", "RelativePath": "api/Campany/department", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "depatment", "Type": "GamalComapany.Service.Dtos.CompanyDto.CearteDepartmentDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.CampanyController", "Method": "UpdateDepartment", "RelativePath": "api/Campany/department", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "depatmentcompany", "Type": "GamalComapany.Service.Dtos.CompanyDto.UpdateDepartmentDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.CampanyController", "Method": "GetListOFDepartment", "RelativePath": "api/Campany/department", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.CategroyController", "Method": "GetCategory", "RelativePath": "api/Categroy", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.API.Controllers.UnitController", "Method": "GetUnits", "RelativePath": "api/Unit", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "GamalComapnyApp.Controllers.WeatherForecastController", "Method": "Get", "RelativePath": "WeatherForecast", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[GamalComapnyApp.WeatherForecast, GamalComapnyApp.API, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "GetWeatherForecast"}]