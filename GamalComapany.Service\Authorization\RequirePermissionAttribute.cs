using Microsoft.AspNetCore.Authorization;

namespace GamalComapany.Service.Authorization
{
    public class RequirePermissionAttribute : AuthorizeAttribute
    {
        public RequirePermissionAttribute(string module, string permission)
        {
            Policy = $"{module}:{permission}";
        }
    }

    // Predefined permission attributes for common operations
    public class RequirePartnerReadAttribute : RequirePermissionAttribute
    {
        public RequirePartnerReadAttribute() : base("Partner", "Read") { }
    }

    public class RequirePartnerWriteAttribute : RequirePermissionAttribute
    {
        public RequirePartnerWriteAttribute() : base("Partner", "Write") { }
    }

    public class RequirePartnerDeleteAttribute : RequirePermissionAttribute
    {
        public RequirePartnerDeleteAttribute() : base("Partner", "Delete") { }
    }

    public class RequireInventoryReadAttribute : RequirePermissionAttribute
    {
        public RequireInventoryReadAttribute() : base("Inventory", "Read") { }
    }

    public class RequireInventoryWriteAttribute : RequirePermissionAttribute
    {
        public RequireInventoryWriteAttribute() : base("Inventory", "Write") { }
    }

    public class RequireFinancialReadAttribute : RequirePermissionAttribute
    {
        public RequireFinancialReadAttribute() : base("Financial", "Read") { }
    }

    public class RequireFinancialWriteAttribute : RequirePermissionAttribute
    {
        public RequireFinancialWriteAttribute() : base("Financial", "Write") { }
    }

    public class RequireUserManagementAttribute : RequirePermissionAttribute
    {
        public RequireUserManagementAttribute() : base("User", "Manage") { }
    }

    public class RequireSystemAdminAttribute : RequirePermissionAttribute
    {
        public RequireSystemAdminAttribute() : base("System", "Admin") { }
    }
}
