using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.Processing;
using SixLabors.ImageSharp.Formats.Jpeg;
using SixLabors.ImageSharp.Formats.Png;
using SixLabors.ImageSharp.Formats.Webp;

namespace GamalComapany.Service.Services
{
    public class ImageService : IImageService
    {
        private readonly IWebHostEnvironment _environment;
        private readonly IConfiguration _configuration;
        private readonly ILogger<ImageService> _logger;
        
        private readonly string _uploadsPath;
        private readonly long _maxFileSize;
        private readonly List<string> _allowedExtensions;
        private readonly List<string> _allowedMimeTypes;

        public ImageService(IWebHostEnvironment environment, IConfiguration configuration, ILogger<ImageService> logger)
        {
            _environment = environment;
            _configuration = configuration;
            _logger = logger;
            
            _uploadsPath = Path.Combine(_environment.WebRootPath, "uploads");
            _maxFileSize = _configuration.GetValue<long>("ImageSettings:MaxFileSizeBytes", 5 * 1024 * 1024); // 5MB default
            _allowedExtensions = _configuration.GetSection("ImageSettings:AllowedExtensions").Get<List<string>>() 
                ?? new List<string> { ".jpg", ".jpeg", ".png", ".webp" };
            _allowedMimeTypes = _configuration.GetSection("ImageSettings:AllowedMimeTypes").Get<List<string>>() 
                ?? new List<string> { "image/jpeg", "image/png", "image/webp" };

            // Ensure uploads directory exists
            Directory.CreateDirectory(_uploadsPath);
            Directory.CreateDirectory(Path.Combine(_uploadsPath, "users"));
            Directory.CreateDirectory(Path.Combine(_uploadsPath, "items"));
            Directory.CreateDirectory(Path.Combine(_uploadsPath, "temp"));
        }

        public async Task<string> UploadImageAsync(IFormFile file, string folder, string? fileName = null)
        {
            try
            {
                if (!await ValidateImageAsync(file))
                    throw new ArgumentException("Invalid image file");

                var folderPath = Path.Combine(_uploadsPath, folder);
                Directory.CreateDirectory(folderPath);

                var uniqueFileName = fileName ?? await GenerateUniqueFileNameAsync(file.FileName);
                var filePath = Path.Combine(folderPath, uniqueFileName);

                using var stream = new FileStream(filePath, FileMode.Create);
                await file.CopyToAsync(stream);

                // Optimize image
                await OptimizeImageAsync(filePath);

                var relativePath = Path.Combine("uploads", folder, uniqueFileName).Replace("\\", "/");
                _logger.LogInformation("Image uploaded successfully: {FilePath}", relativePath);
                
                return relativePath;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading image: {FileName}", file.FileName);
                throw;
            }
        }

        public async Task<string> UploadUserProfileImageAsync(IFormFile file, int userId)
        {
            try
            {
                // Delete existing profile image if exists
                await DeleteUserProfileImageAsync(userId);

                var fileName = $"user_{userId}_{DateTime.UtcNow:yyyyMMddHHmmss}{Path.GetExtension(file.FileName)}";
                return await UploadImageAsync(file, "users", fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading user profile image for user: {UserId}", userId);
                throw;
            }
        }

        public async Task<string> UploadItemImageAsync(IFormFile file, int itemId, bool isMain = false)
        {
            try
            {
                var prefix = isMain ? "main" : "gallery";
                var timestamp = DateTime.UtcNow.ToString("yyyyMMddHHmmss");
                var fileName = $"item_{itemId}_{prefix}_{timestamp}{Path.GetExtension(file.FileName)}";
                
                return await UploadImageAsync(file, "items", fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading item image for item: {ItemId}", itemId);
                throw;
            }
        }

        public async Task<bool> DeleteImageAsync(string imagePath)
        {
            try
            {
                if (string.IsNullOrEmpty(imagePath))
                    return false;

                var fullPath = Path.Combine(_environment.WebRootPath, imagePath.Replace("/", "\\"));
                
                if (File.Exists(fullPath))
                {
                    File.Delete(fullPath);
                    _logger.LogInformation("Image deleted successfully: {ImagePath}", imagePath);
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting image: {ImagePath}", imagePath);
                return false;
            }
        }

        public async Task<bool> DeleteUserProfileImageAsync(int userId)
        {
            try
            {
                var userFolder = Path.Combine(_uploadsPath, "users");
                var userImages = Directory.GetFiles(userFolder, $"user_{userId}_*");
                
                foreach (var imagePath in userImages)
                {
                    File.Delete(imagePath);
                }

                _logger.LogInformation("User profile images deleted for user: {UserId}", userId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting user profile images for user: {UserId}", userId);
                return false;
            }
        }

        public async Task<bool> DeleteItemImageAsync(string imagePath)
        {
            return await DeleteImageAsync(imagePath);
        }

        public async Task<byte[]?> GetImageAsync(string imagePath)
        {
            try
            {
                if (string.IsNullOrEmpty(imagePath))
                    return null;

                var fullPath = Path.Combine(_environment.WebRootPath, imagePath.Replace("/", "\\"));
                
                if (File.Exists(fullPath))
                {
                    return await File.ReadAllBytesAsync(fullPath);
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting image: {ImagePath}", imagePath);
                return null;
            }
        }

        public async Task<string> ResizeImageAsync(string imagePath, int width, int height)
        {
            try
            {
                var fullPath = Path.Combine(_environment.WebRootPath, imagePath.Replace("/", "\\"));
                
                if (!File.Exists(fullPath))
                    throw new FileNotFoundException("Image not found");

                var directory = Path.GetDirectoryName(fullPath);
                var fileName = Path.GetFileNameWithoutExtension(fullPath);
                var extension = Path.GetExtension(fullPath);
                var resizedFileName = $"{fileName}_{width}x{height}{extension}";
                var resizedPath = Path.Combine(directory!, resizedFileName);

                using var image = await Image.LoadAsync(fullPath);
                image.Mutate(x => x.Resize(width, height));
                await image.SaveAsync(resizedPath);

                var relativePath = Path.GetRelativePath(_environment.WebRootPath, resizedPath).Replace("\\", "/");
                return relativePath;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error resizing image: {ImagePath}", imagePath);
                throw;
            }
        }

        public async Task<List<string>> GetSupportedFormats()
        {
            return await Task.FromResult(_allowedExtensions);
        }

        public async Task<bool> ValidateImageAsync(IFormFile file)
        {
            try
            {
                if (file == null || file.Length == 0)
                    return false;

                // Check file size
                if (file.Length > _maxFileSize)
                {
                    _logger.LogWarning("File size exceeds limit: {FileSize} bytes", file.Length);
                    return false;
                }

                // Check file extension
                var extension = Path.GetExtension(file.FileName).ToLowerInvariant();
                if (!_allowedExtensions.Contains(extension))
                {
                    _logger.LogWarning("File extension not allowed: {Extension}", extension);
                    return false;
                }

                // Check MIME type
                if (!_allowedMimeTypes.Contains(file.ContentType.ToLowerInvariant()))
                {
                    _logger.LogWarning("MIME type not allowed: {MimeType}", file.ContentType);
                    return false;
                }

                // Validate image content
                try
                {
                    using var stream = file.OpenReadStream();
                    using var image = await Image.LoadAsync(stream);
                    
                    // Additional validation can be added here
                    if (image.Width < 1 || image.Height < 1)
                        return false;
                }
                catch
                {
                    _logger.LogWarning("Invalid image content: {FileName}", file.FileName);
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating image: {FileName}", file.FileName);
                return false;
            }
        }

        public async Task<long> GetMaxFileSizeAsync()
        {
            return await Task.FromResult(_maxFileSize);
        }

        public async Task<string> GenerateUniqueFileNameAsync(string originalFileName)
        {
            var extension = Path.GetExtension(originalFileName);
            var uniqueId = Guid.NewGuid().ToString("N")[..8];
            var timestamp = DateTime.UtcNow.ToString("yyyyMMddHHmmss");
            
            return await Task.FromResult($"{timestamp}_{uniqueId}{extension}");
        }

        public async Task<bool> ImageExistsAsync(string imagePath)
        {
            try
            {
                if (string.IsNullOrEmpty(imagePath))
                    return false;

                var fullPath = Path.Combine(_environment.WebRootPath, imagePath.Replace("/", "\\"));
                return await Task.FromResult(File.Exists(fullPath));
            }
            catch
            {
                return false;
            }
        }

        public async Task CleanupOrphanedImagesAsync()
        {
            try
            {
                var tempFolder = Path.Combine(_uploadsPath, "temp");
                var cutoffDate = DateTime.UtcNow.AddDays(-1); // Delete temp files older than 1 day

                if (Directory.Exists(tempFolder))
                {
                    var tempFiles = Directory.GetFiles(tempFolder)
                        .Where(f => File.GetCreationTime(f) < cutoffDate);

                    foreach (var file in tempFiles)
                    {
                        File.Delete(file);
                    }
                }

                _logger.LogInformation("Orphaned images cleanup completed");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during orphaned images cleanup");
            }
        }

        private async Task OptimizeImageAsync(string filePath)
        {
            try
            {
                using var image = await Image.LoadAsync(filePath);
                
                // Optimize based on format
                var extension = Path.GetExtension(filePath).ToLowerInvariant();
                
                switch (extension)
                {
                    case ".jpg":
                    case ".jpeg":
                        await image.SaveAsync(filePath, new JpegEncoder { Quality = 85 });
                        break;
                    case ".png":
                        await image.SaveAsync(filePath, new PngEncoder { CompressionLevel = PngCompressionLevel.BestCompression });
                        break;
                    case ".webp":
                        await image.SaveAsync(filePath, new WebpEncoder { Quality = 85 });
                        break;
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Could not optimize image: {FilePath}", filePath);
                // Continue without optimization if it fails
            }
        }
    }
}
