using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;
using System.Security.Claims;

namespace GamalComapany.Service.Authorization
{
    public class PermissionAuthorizationHandler : AuthorizationHandler<PermissionRequirement>
    {
        private readonly ILogger<PermissionAuthorizationHandler> _logger;

        public PermissionAuthorizationHandler(ILogger<PermissionAuthorizationHandler> logger)
        {
            _logger = logger;
        }

        protected override Task HandleRequirementAsync(
            AuthorizationHandlerContext context,
            PermissionRequirement requirement)
        {
            try
            {
                // Check if user is authenticated
                if (!context.User.Identity?.IsAuthenticated ?? true)
                {
                    _logger.LogWarning("Authorization failed: User not authenticated");
                    context.Fail();
                    return Task.CompletedTask;
                }

                // Get user permissions from claims
                var permissions = context.User.Claims
                    .Where(c => c.Type == "permission")
                    .Select(c => c.Value)
                    .ToList();

                // Check if user has the required permission
                var requiredPermission = $"{requirement.Module}:{requirement.Permission}";
                var hasPermission = permissions.Contains(requiredPermission);

                if (hasPermission)
                {
                    _logger.LogDebug("Authorization succeeded for permission: {Permission}", requiredPermission);
                    context.Succeed(requirement);
                }
                else
                {
                    var userId = context.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                    _logger.LogWarning("Authorization failed for user {UserId}: Missing permission {Permission}", 
                        userId, requiredPermission);
                    context.Fail();
                }

                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during authorization check");
                context.Fail();
                return Task.CompletedTask;
            }
        }
    }
}
