﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GamalComapany.Service.Repositories.Interfaces
{
    public interface IUnitOfWork
    {
        // Existing Repositories
        IItemRepository ItemRepository { get; }
        IUintRepository UintRepository { get; }
        ICategoryRepository CategoryRepository { get; }
        ICompanyRepository CompanyRepository { get; }

        // New Repositories
        IPartnerRepository PartnerRepository { get; }
        IInventoryRepository InventoryRepository { get; }
        IFinancialRepository FinancialRepository { get; }
        ISupplierCustomerRepository SupplierCustomerRepository { get; }
        IUserRepository UserRepository { get; }
    }
}
