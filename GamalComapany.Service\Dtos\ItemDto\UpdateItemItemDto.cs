using System.ComponentModel.DataAnnotations;

namespace GamalComapany.Service.Dtos.ItemDto
{
    public class UpdateItemItemDto
    {
        [Required(ErrorMessage = "معرف الصنف مطلوب")]
        public int Id { get; set; }

        [Required(ErrorMessage = "الاسم الإنجليزي مطلوب")]
        [StringLength(100, ErrorMessage = "الاسم الإنجليزي يجب أن يكون أقل من 100 حرف")]
        public string NameEn { get; set; } = string.Empty;

        [StringLength(100, ErrorMessage = "الاسم العربي يجب أن يكون أقل من 100 حرف")]
        public string? NameAr { get; set; }

        [StringLength(500, ErrorMessage = "الوصف يجب أن يكون أقل من 500 حرف")]
        public string? Description { get; set; }

        [StringLength(50, ErrorMessage = "كود الصنف يجب أن يكون أقل من 50 حرف")]
        public string? ItemCode { get; set; }

        [Required(ErrorMessage = "معرف التصنيف مطلوب")]
        public int CategoryId { get; set; }

        [StringLength(100, ErrorMessage = "الباركود يجب أن يكون أقل من 100 حرف")]
        public string? Barcode { get; set; }

        [Required(ErrorMessage = "معرف الوحدة مطلوب")]
        public int UnitId { get; set; }

        [Range(0, double.MaxValue, ErrorMessage = "التكلفة المعيارية يجب أن تكون أكبر من أو تساوي صفر")]
        public decimal StandardCost { get; set; }

        [Range(0, int.MaxValue, ErrorMessage = "الحد الأدنى للمخزون يجب أن يكون أكبر من أو يساوي صفر")]
        public int? MinimumStock { get; set; }

        [Range(0, int.MaxValue, ErrorMessage = "الحد الأقصى للمخزون يجب أن يكون أكبر من أو يساوي صفر")]
        public int? MaximumStock { get; set; }

        [Range(0, int.MaxValue, ErrorMessage = "مستوى إعادة الطلب يجب أن يكون أكبر من أو يساوي صفر")]
        public int? ReorderLevel { get; set; }

        public int? SortOrder { get; set; }

        [StringLength(50, ErrorMessage = "نوع الصنف يجب أن يكون أقل من 50 حرف")]
        public string? ItemType { get; set; }

        public DateTime? UpdatedAt { get; set; }
    }
}
