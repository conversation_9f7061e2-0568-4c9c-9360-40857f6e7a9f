{"ConnectionStrings": {"DefaultConnection": "Data Source=DESKTOP-G1KQMAJ\\SQLEXPRESS;Initial Catalog=DoorCompanyDb;User ID=***;Password=***;TrustServerCertificate=True;MultipleActiveResultSets=True;Integrated Security=False"}, "JwtSettings": {"SecretKey": "YourSuperSecretKeyThatIsAtLeast32CharactersLongForSecurity!@#$%^&*()", "Issuer": "GamalCompanyApp", "Audience": "GamalCompanyApp", "AccessTokenExpirationMinutes": 60, "RefreshTokenExpirationDays": 7, "ValidateIssuer": true, "ValidateAudience": true, "ValidateLifetime": true, "ValidateIssuerSigningKey": true, "ClockSkewMinutes": 5}, "ImageSettings": {"MaxFileSizeBytes": 5242880, "AllowedExtensions": [".jpg", ".jpeg", ".png", ".webp"], "AllowedMimeTypes": ["image/jpeg", "image/png", "image/webp"], "UploadPath": "wwwroot/uploads", "ThumbnailSizes": [{"Width": 150, "Height": 150, "Name": "thumbnail"}, {"Width": 300, "Height": 300, "Name": "medium"}, {"Width": 800, "Height": 600, "Name": "large"}]}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*"}