using GamalComapany.Service.Authorization;
using GamalComapany.Service.Dtos.PartnerDto;
using GamalComapany.Service.Repositories.Interfaces;
using GamalComapnyApp.API.Base;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace GamalComapnyApp.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class PartnerController : AppControllerBase
    {
        public PartnerController(IUnitOfWork work) : base(work)
        {
        }

        /// <summary>
        /// Get all partners
        /// </summary>
        [HttpGet]
        [RequirePartnerRead]
        public async Task<IActionResult> GetAllPartners()
        {
            var response = await _work.PartnerRepository.GetAllPartnersAsync();
            return NewResult(response);
        }

        /// <summary>
        /// Get partner by ID
        /// </summary>
        [HttpGet("{id}")]
        [RequirePartnerRead]
        public async Task<IActionResult> GetPartnerById(int id)
        {
            var response = await _work.PartnerRepository.GetPartnerByIdAsync(id);
            return NewResult(response);
        }

        /// <summary>
        /// Create new partner
        /// </summary>
        [HttpPost]
        [RequirePartnerWrite]
        public async Task<IActionResult> CreatePartner(CreatePartnerDto createPartnerDto)
        {
            var response = await _work.PartnerRepository.CreatePartnerAsync(createPartnerDto);
            return NewResult(response);
        }

        /// <summary>
        /// Update partner
        /// </summary>
        [HttpPut]
        [RequirePartnerWrite]
        public async Task<IActionResult> UpdatePartner(UpdatePartnerDto updatePartnerDto)
        {
            var response = await _work.PartnerRepository.UpdatePartnerAsync(updatePartnerDto);
            return NewResult(response);
        }

        /// <summary>
        /// Delete partner
        /// </summary>
        [HttpDelete("{id}")]
        [RequirePartnerDelete]
        public async Task<IActionResult> DeletePartner(int id)
        {
            var response = await _work.PartnerRepository.DeletePartnerAsync(id);
            return NewResult(response);
        }

        /// <summary>
        /// Calculate partners capital
        /// </summary>
        [HttpGet("capital-calculation")]
        [RequirePartnerRead]
        public async Task<IActionResult> CalculatePartnersCapital()
        {
            var response = await _work.PartnerRepository.CalculatePartnersCapitalAsync();
            return NewResult(response);
        }

        /// <summary>
        /// Recalculate share percentages based on current capital
        /// </summary>
        [HttpPost("recalculate-shares")]
        [RequirePartnerWrite]
        public async Task<IActionResult> RecalculateSharePercentages()
        {
            var response = await _work.PartnerRepository.RecalculateSharePercentagesAsync();
            return NewResult(response);
        }

        /// <summary>
        /// Calculate profit distribution
        /// </summary>
        [HttpPost("profit-distribution")]
        [RequirePartnerRead]
        public async Task<IActionResult> CalculateProfitDistribution([FromBody] decimal totalProfit)
        {
            var response = await _work.PartnerRepository.CalculateProfitDistributionAsync(totalProfit);
            return NewResult(response);
        }

        /// <summary>
        /// Get partner transactions
        /// </summary>
        [HttpGet("{partnerId}/transactions")]
        [RequirePartnerRead]
        public async Task<IActionResult> GetPartnerTransactions(int partnerId)
        {
            var response = await _work.PartnerRepository.GetPartnerTransactionsAsync(partnerId);
            return NewResult(response);
        }

        /// <summary>
        /// Create partner transaction
        /// </summary>
        [HttpPost("transactions")]
        [RequirePartnerWrite]
        public async Task<IActionResult> CreatePartnerTransaction(PartnerTransactionDto transactionDto)
        {
            var response = await _work.PartnerRepository.CreatePartnerTransactionAsync(transactionDto);
            return NewResult(response);
        }

        /// <summary>
        /// Delete partner transaction
        /// </summary>
        [HttpDelete("transactions/{transactionId}")]
        [RequirePartnerDelete]
        public async Task<IActionResult> DeletePartnerTransaction(int transactionId)
        {
            var response = await _work.PartnerRepository.DeletePartnerTransactionAsync(transactionId);
            return NewResult(response);
        }
    }
}
