﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GamalComapany.Service.Dtos.ItemDto
{
    public class ItemDto
    {
        public int Id { get; set; }
        public string NameEn { get; set; } = string.Empty;

        [Required(ErrorMessage = "من فضلك ادخل الاسم")]
        public string? NameAr { get; set; }

        [Required(ErrorMessage = "من فضلك ادخل الوصف")]
        public string Description { get; set; } = string.Empty;

        public string? ItemCode { get; set; }

        [Required(ErrorMessage = "اختار التصنيف")]
        public int CategoryId { get; set; }

        public string CategoryName { get; set; } = string.Empty;

        [Required(ErrorMessage = "من فضلك ادخل الباركود")]
        [StringLength(50, ErrorMessage = "الباركود يجب ألا يزيد عن 50 حرف")]
        public string Barcode { get; set; } = string.Empty;
      
        [Required(ErrorMessage = "الوحدة الرئيسية مطلوبة")]
        public int UnitId { get; set; }

        [Column(TypeName = "decimal(18,4)")]
        [Range(0, double.MaxValue, ErrorMessage = "التكلفة الاوليه يجب ان أكبر من أو يساوي صفر")]
        public decimal StandardCost { get; set; }//التكلفة القياسية

        [Range(0, double.MaxValue, ErrorMessage = "الرصيد الحالي يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal CurrentStock { get; set; } = 0;

        [Column(TypeName = "decimal(18,4)")]
        [Range(0, double.MaxValue, ErrorMessage = "الحد الأدنى للمخزون يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal? MinimumStock { get; set; }//الحد الأدنى للمخزون

        [Column(TypeName = "decimal(18,4)")]
        [Range(0, double.MaxValue, ErrorMessage = "الحد الاقصي للمخزون يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal? MaximumStock { get; set; }// الحد الأقصى للمخزون

        [Column(TypeName = "decimal(18,4)")]
        public decimal? ReorderLevel { get; set; }//إعادة ترتيب المستوى
        public int? SortOrder { get; set; } = 1;
        public int? ItemType { get; set; } = 1; //item //Product
    }
}
