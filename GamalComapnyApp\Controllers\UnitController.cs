﻿using GamalComapany.Service.Repositories.Interfaces;
using GamalComapnyApp.API.Base;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace GamalComapnyApp.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class UnitController : AppControllerBase
    {
        public UnitController(IUnitOfWork work) : base(work)
        {
                
        }
        [HttpGet]
        public async Task<IActionResult> GetUnits()
        {
            var response = await _work.UintRepository.GetUnit();
            return NewResult(response);
        }


    }
}
