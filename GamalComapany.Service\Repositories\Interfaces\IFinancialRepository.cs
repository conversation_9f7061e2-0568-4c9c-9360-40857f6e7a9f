using GamalComapany.Service.Dtos;
using GamalComapany.Service.Dtos.FinancialDto;

namespace GamalComapany.Service.Repositories.Interfaces
{
    public interface IFinancialRepository
    {
        // Treasury Management
        Task<ApiResponse<List<TreasuryResponseDto>>> GetAllTreasuriesAsync();
        Task<ApiResponse<TreasuryResponseDto>> GetTreasuryByIdAsync(int id);
        Task<ApiResponse<TreasuryResponseDto>> CreateTreasuryAsync(CreateTreasuryDto treasuryDto);
        Task<ApiResponse<TreasuryResponseDto>> UpdateTreasuryAsync(UpdateTreasuryDto treasuryDto);
        Task<ApiResponse<bool>> DeleteTreasuryAsync(int id);
        
        // Treasury Transactions
        Task<ApiResponse<List<TreasuryTransactionResponseDto>>> GetTreasuryTransactionsAsync(int? treasuryId = null, DateTime? fromDate = null, DateTime? toDate = null);
        Task<ApiResponse<TreasuryTransactionResponseDto>> CreateTreasuryTransactionAsync(CreateTreasuryTransactionDto transactionDto);
        Task<ApiResponse<bool>> DeleteTreasuryTransactionAsync(int transactionId);
        
        // Financial Transactions
        Task<ApiResponse<List<FinancialTransactionResponseDto>>> GetFinancialTransactionsAsync(DateTime? fromDate = null, DateTime? toDate = null);
        Task<ApiResponse<FinancialTransactionResponseDto>> CreateFinancialTransactionAsync(CreateFinancialTransactionDto transactionDto);
        Task<ApiResponse<bool>> DeleteFinancialTransactionAsync(int transactionId);
        
        // Treasury Balance and Reports
        Task<ApiResponse<TreasuryBalanceDto>> GetTreasuryBalanceAsync(int treasuryId, DateTime fromDate, DateTime toDate);
        Task<ApiResponse<List<TreasuryBalanceDto>>> GetAllTreasuriesBalanceAsync(DateTime fromDate, DateTime toDate);
        Task<ApiResponse<CashFlowDto>> GetCashFlowReportAsync(DateTime fromDate, DateTime toDate);
        
        // Treasury Balance Calculations
        Task<ApiResponse<decimal>> GetTreasuryCurrentBalanceAsync(int treasuryId);
        Task<ApiResponse<decimal>> GetTotalCashBalanceAsync();
    }
}
