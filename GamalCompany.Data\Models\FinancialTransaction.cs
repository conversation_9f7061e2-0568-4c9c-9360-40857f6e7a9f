﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GamalCompany.Data.Models
{
    public class FinancialTransaction : BaseEntity
    {
        public int TransactionTypeId { get; set; }
        public DateTime TransactionDate { get; set; }
       
        [Column(TypeName = "decimal(18,4)")]
        public decimal? Amount { get; set; }  
        public string? Description { get; set; } //فاتورة بيع رقم #{InvoiceNumber}  
        public int? InvoiceNumber { get; set; } //InvoiceId

      
        [ForeignKey(nameof(TransactionTypeId))]
        public MainAction TransactionType { get; set; } = null!;
    
    }
}
