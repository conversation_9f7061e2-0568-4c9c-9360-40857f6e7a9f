using Microsoft.AspNetCore.Authorization;

namespace GamalComapany.Service.Authorization
{
    public class PermissionRequirement : IAuthorizationRequirement
    {
        public string Module { get; }
        public string Permission { get; }

        public PermissionRequirement(string module, string permission)
        {
            Module = module;
            Permission = permission;
        }
    }
}
