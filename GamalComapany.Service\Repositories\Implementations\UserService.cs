using AutoMapper;
using GamalComapany.Service.Authentication;
using GamalComapany.Service.Dtos;
using GamalComapany.Service.Dtos.UserDto;
using GamalComapany.Service.Repositories.Interfaces;
using GamalCompany.Data.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace GamalComapany.Service.Repositories.Implementations
{
    public class UserService : ResponseHandler, IUserRepository
    {
        private readonly IUnitOfWorkOfService _unitOfWork;
        private readonly IMapper _mapper;
        private readonly ILogger<UserService> _logger;
        private readonly IPasswordHashingService _passwordHashingService;
        private readonly IJwtTokenService _jwtTokenService;

        public UserService(
            IUnitOfWorkOfService unitOfWork, 
            IMapper mapper, 
            ILogger<UserService> logger,
            IPasswordHashingService passwordHashingService,
            IJwtTokenService jwtTokenService)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
            _logger = logger;
            _passwordHashingService = passwordHashingService;
            _jwtTokenService = jwtTokenService;
        }

        public async Task<ApiResponse<List<UserResponseDto>>> GetAllUsersAsync()
        {
            try
            {
                var users = await _unitOfWork.Users.GetTableNoTracking()
                    .Where(u => !u.IsDeleted)
                    .Include(u => u.UserPermissions)
                    .ThenInclude(up => up.Module)
                    .ToListAsync();

                if (!users.Any())
                    return NotFound<List<UserResponseDto>>("لا توجد مستخدمين");

                var userDtos = _mapper.Map<List<UserResponseDto>>(users);
                return Success(userDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all users");
                return BadRequest<List<UserResponseDto>>("حدث خطأ أثناء جلب بيانات المستخدمين");
            }
        }

        public async Task<ApiResponse<UserResponseDto>> GetUserByIdAsync(int id)
        {
            try
            {
                var user = await _unitOfWork.Users.GetTableNoTracking()
                    .Include(u => u.UserPermissions)
                    .ThenInclude(up => up.Module)
                    .FirstOrDefaultAsync(u => u.Id == id && !u.IsDeleted);

                if (user == null)
                    return NotFound<UserResponseDto>("المستخدم غير موجود");

                var userDto = _mapper.Map<UserResponseDto>(user);
                return Success(userDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user by id: {UserId}", id);
                return BadRequest<UserResponseDto>("حدث خطأ أثناء جلب بيانات المستخدم");
            }
        }

        public async Task<ApiResponse<UserResponseDto>> GetUserByUsernameAsync(string username)
        {
            try
            {
                var user = await _unitOfWork.Users.GetTableNoTracking()
                    .Include(u => u.UserPermissions)
                    .ThenInclude(up => up.Module)
                    .FirstOrDefaultAsync(u => u.UserName == username && !u.IsDeleted);

                if (user == null)
                    return NotFound<UserResponseDto>("المستخدم غير موجود");

                var userDto = _mapper.Map<UserResponseDto>(user);
                return Success(userDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user by username: {Username}", username);
                return BadRequest<UserResponseDto>("حدث خطأ أثناء جلب بيانات المستخدم");
            }
        }

        public async Task<ApiResponse<UserResponseDto>> CreateUserAsync(CreateUserDto userDto)
        {
            try
            {
                // Check if username already exists
                var existingUser = await _unitOfWork.Users.GetTableNoTracking()
                    .FirstOrDefaultAsync(u => u.UserName == userDto.UserName && !u.IsDeleted);

                if (existingUser != null)
                    return BadRequest<UserResponseDto>("اسم المستخدم موجود بالفعل");

                var user = _mapper.Map<User>(userDto);
                user.Password = _passwordHashingService.HashPassword(userDto.Password);
                user.CreatedAt = DateTime.Now;

                await _unitOfWork.Users.AddAsync(user);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("User created successfully: {Username}", user.UserName);

                var result = await GetUserByIdAsync(user.Id);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating user: {Username}", userDto.UserName);
                return BadRequest<UserResponseDto>("حدث خطأ أثناء إنشاء المستخدم");
            }
        }

        public async Task<ApiResponse<UserResponseDto>> UpdateUserAsync(UpdateUserDto userDto)
        {
            try
            {
                var existingUser = await _unitOfWork.Users.GetByIdAsync(userDto.Id);
                if (existingUser == null || existingUser.IsDeleted)
                    return NotFound<UserResponseDto>("المستخدم غير موجود");

                // Check if username is taken by another user
                var duplicateUser = await _unitOfWork.Users.GetTableNoTracking()
                    .FirstOrDefaultAsync(u => u.UserName == userDto.UserName && u.Id != userDto.Id && !u.IsDeleted);

                if (duplicateUser != null)
                    return BadRequest<UserResponseDto>("اسم المستخدم موجود بالفعل");

                _mapper.Map(userDto, existingUser);
                existingUser.UpdatedAt = DateTime.Now;

                await _unitOfWork.Users.UpdateAsync(existingUser);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("User updated successfully: {UserId}", existingUser.Id);

                var result = await GetUserByIdAsync(existingUser.Id);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating user: {UserId}", userDto.Id);
                return BadRequest<UserResponseDto>("حدث خطأ أثناء تحديث المستخدم");
            }
        }

        public async Task<ApiResponse<bool>> DeleteUserAsync(int id)
        {
            try
            {
                var user = await _unitOfWork.Users.GetByIdAsync(id);
                if (user == null || user.IsDeleted)
                    return NotFound<bool>("المستخدم غير موجود");

                // Soft delete
                user.IsDeleted = true;
                user.IsActive = false;
                user.UpdatedAt = DateTime.Now;

                await _unitOfWork.Users.UpdateAsync(user);

                // Revoke all refresh tokens
                await _jwtTokenService.RevokeAllUserTokensAsync(id);

                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("User deleted successfully: {UserId}", id);
                return Success(true, "تم حذف المستخدم بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting user: {UserId}", id);
                return BadRequest<bool>("حدث خطأ أثناء حذف المستخدم");
            }
        }

        public async Task<ApiResponse<bool>> ChangePasswordAsync(ChangePasswordDto changePasswordDto)
        {
            try
            {
                var user = await _unitOfWork.Users.GetByIdAsync(changePasswordDto.UserId);
                if (user == null || user.IsDeleted)
                    return NotFound<bool>("المستخدم غير موجود");

                // Verify current password
                if (!_passwordHashingService.VerifyPassword(changePasswordDto.CurrentPassword, user.Password))
                    return BadRequest<bool>("كلمة المرور الحالية غير صحيحة");

                // Hash new password
                user.Password = _passwordHashingService.HashPassword(changePasswordDto.NewPassword);
                user.UpdatedAt = DateTime.Now;

                await _unitOfWork.Users.UpdateAsync(user);

                // Revoke all refresh tokens to force re-login
                await _jwtTokenService.RevokeAllUserTokensAsync(user.Id);

                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("Password changed successfully for user: {UserId}", user.Id);
                return Success(true, "تم تغيير كلمة المرور بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error changing password for user: {UserId}", changePasswordDto.UserId);
                return BadRequest<bool>("حدث خطأ أثناء تغيير كلمة المرور");
            }
        }

        public async Task<ApiResponse<bool>> ResetPasswordAsync(int userId, string newPassword)
        {
            try
            {
                var user = await _unitOfWork.Users.GetByIdAsync(userId);
                if (user == null || user.IsDeleted)
                    return NotFound<bool>("المستخدم غير موجود");

                user.Password = _passwordHashingService.HashPassword(newPassword);
                user.UpdatedAt = DateTime.Now;

                await _unitOfWork.Users.UpdateAsync(user);

                // Revoke all refresh tokens
                await _jwtTokenService.RevokeAllUserTokensAsync(userId);

                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("Password reset successfully for user: {UserId}", userId);
                return Success(true, "تم إعادة تعيين كلمة المرور بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error resetting password for user: {UserId}", userId);
                return BadRequest<bool>("حدث خطأ أثناء إعادة تعيين كلمة المرور");
            }
        }

        // Authentication Methods
        public async Task<ApiResponse<LoginResponseDto>> LoginAsync(LoginDto loginDto)
        {
            try
            {
                var user = await _unitOfWork.Users.GetTableNoTracking()
                    .Include(u => u.UserPermissions)
                    .ThenInclude(up => up.Module)
                    .FirstOrDefaultAsync(u => u.UserName == loginDto.UserName && !u.IsDeleted);

                if (user == null || !user.IsActive)
                {
                    _logger.LogWarning("Login attempt with invalid username: {Username}", loginDto.UserName);
                    return Unauthorized<LoginResponseDto>("اسم المستخدم أو كلمة المرور غير صحيحة");
                }

                if (!_passwordHashingService.VerifyPassword(loginDto.Password, user.Password))
                {
                    _logger.LogWarning("Login attempt with invalid password for user: {Username}", loginDto.UserName);
                    return Unauthorized<LoginResponseDto>("اسم المستخدم أو كلمة المرور غير صحيحة");
                }

                // Get user permissions
                var permissions = user.UserPermissions?
                    .Where(up => up.IsActive && !up.IsDeleted)
                    .Select(up => $"{up.Module?.NameEn}:{up.Permission}")
                    .ToList() ?? new List<string>();

                // Generate tokens
                var accessToken = await _jwtTokenService.GenerateAccessTokenAsync(user, permissions);
                var refreshToken = await _jwtTokenService.GenerateRefreshTokenAsync();

                // Store refresh token
                var refreshTokenEntity = new RefreshToken
                {
                    Token = refreshToken,
                    UserId = user.Id,
                    ExpiryDate = DateTime.UtcNow.AddDays(7), // 7 days
                    CreatedAt = DateTime.Now
                };

                await _unitOfWork.RefreshTokens.AddAsync(refreshTokenEntity);
                await _unitOfWork.SaveChangesAsync();

                var loginResponse = new LoginResponseDto
                {
                    UserId = user.Id,
                    UserName = user.UserName,
                    FullName = user.FullName,
                    AccessToken = accessToken,
                    RefreshToken = refreshToken,
                    TokenExpiry = DateTime.UtcNow.AddMinutes(60), // 1 hour
                    Permissions = permissions
                };

                _logger.LogInformation("User logged in successfully: {Username}", user.UserName);
                return Success(loginResponse, "تم تسجيل الدخول بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during login for user: {Username}", loginDto.UserName);
                return BadRequest<LoginResponseDto>("حدث خطأ أثناء تسجيل الدخول");
            }
        }

        public async Task<ApiResponse<LoginResponseDto>> RefreshTokenAsync(RefreshTokenDto refreshTokenDto)
        {
            try
            {
                var storedToken = await _unitOfWork.RefreshTokens.GetTableNoTracking()
                    .Include(rt => rt.User)
                    .ThenInclude(u => u.UserPermissions)
                    .ThenInclude(up => up.Module)
                    .FirstOrDefaultAsync(rt => rt.Token == refreshTokenDto.RefreshToken && !rt.IsRevoked && rt.ExpiryDate > DateTime.UtcNow);

                if (storedToken == null)
                {
                    _logger.LogWarning("Invalid refresh token used");
                    return Unauthorized<LoginResponseDto>("رمز التحديث غير صالح");
                }

                if (!storedToken.User.IsActive || storedToken.User.IsDeleted)
                {
                    _logger.LogWarning("Refresh token used for inactive user: {UserId}", storedToken.UserId);
                    return Unauthorized<LoginResponseDto>("المستخدم غير نشط");
                }

                // Generate new access token
                var newAccessToken = await _jwtTokenService.RefreshAccessTokenAsync(refreshTokenDto.RefreshToken);

                // Get user permissions
                var permissions = storedToken.User.UserPermissions?
                    .Where(up => up.IsActive && !up.IsDeleted)
                    .Select(up => $"{up.Module?.NameEn}:{up.Permission}")
                    .ToList() ?? new List<string>();

                var loginResponse = new LoginResponseDto
                {
                    UserId = storedToken.User.Id,
                    UserName = storedToken.User.UserName,
                    FullName = storedToken.User.FullName,
                    AccessToken = newAccessToken,
                    RefreshToken = refreshTokenDto.RefreshToken, // Keep the same refresh token
                    TokenExpiry = DateTime.UtcNow.AddMinutes(60),
                    Permissions = permissions
                };

                _logger.LogInformation("Token refreshed successfully for user: {UserId}", storedToken.UserId);
                return Success(loginResponse, "تم تحديث الرمز بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error refreshing token");
                return BadRequest<LoginResponseDto>("حدث خطأ أثناء تحديث الرمز");
            }
        }

        public async Task<ApiResponse<bool>> LogoutAsync(int userId)
        {
            try
            {
                await _jwtTokenService.RevokeAllUserTokensAsync(userId);
                _logger.LogInformation("User logged out successfully: {UserId}", userId);
                return Success(true, "تم تسجيل الخروج بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during logout for user: {UserId}", userId);
                return BadRequest<bool>("حدث خطأ أثناء تسجيل الخروج");
            }
        }

        public async Task<ApiResponse<bool>> ValidateTokenAsync(string token)
        {
            try
            {
                var principal = await _jwtTokenService.ValidateTokenAsync(token);
                return Success(principal != null, principal != null ? "الرمز صالح" : "الرمز غير صالح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating token");
                return BadRequest<bool>("حدث خطأ أثناء التحقق من الرمز");
            }
        }

        // Permission Management Methods
        public async Task<ApiResponse<List<UserPermissionResponseDto>>> GetUserPermissionsAsync(int userId)
        {
            try
            {
                var permissions = await _unitOfWork.UserPermissions.GetTableNoTracking()
                    .Include(up => up.Module)
                    .Where(up => up.UserId == userId && !up.IsDeleted)
                    .ToListAsync();

                var permissionDtos = _mapper.Map<List<UserPermissionResponseDto>>(permissions);
                return Success(permissionDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user permissions: {UserId}", userId);
                return BadRequest<List<UserPermissionResponseDto>>("حدث خطأ أثناء جلب صلاحيات المستخدم");
            }
        }

        public async Task<ApiResponse<UserPermissionResponseDto>> CreateUserPermissionAsync(CreateUserPermissionDto permissionDto)
        {
            try
            {
                // Check if user exists
                var user = await _unitOfWork.Users.GetByIdAsync(permissionDto.UserId);
                if (user == null || user.IsDeleted)
                    return NotFound<UserPermissionResponseDto>("المستخدم غير موجود");

                // Check if module exists
                var module = await _unitOfWork.Modules.GetByIdAsync(permissionDto.ModuleId);
                if (module == null || module.IsDeleted)
                    return NotFound<UserPermissionResponseDto>("الوحدة غير موجودة");

                // Check if permission already exists
                var existingPermission = await _unitOfWork.UserPermissions.GetTableNoTracking()
                    .FirstOrDefaultAsync(up => up.UserId == permissionDto.UserId &&
                                              up.ModuleId == permissionDto.ModuleId &&
                                              up.Permission == permissionDto.Permission &&
                                              !up.IsDeleted);

                if (existingPermission != null)
                    return BadRequest<UserPermissionResponseDto>("الصلاحية موجودة بالفعل");

                var permission = _mapper.Map<UserPermission>(permissionDto);
                permission.CreatedAt = DateTime.Now;

                await _unitOfWork.UserPermissions.AddAsync(permission);
                await _unitOfWork.SaveChangesAsync();

                var result = _mapper.Map<UserPermissionResponseDto>(permission);
                result.ModuleName = module.NameEn;

                _logger.LogInformation("User permission created: {UserId} - {ModuleId} - {Permission}",
                    permissionDto.UserId, permissionDto.ModuleId, permissionDto.Permission);

                return Success(result, "تم إنشاء الصلاحية بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating user permission");
                return BadRequest<UserPermissionResponseDto>("حدث خطأ أثناء إنشاء الصلاحية");
            }
        }

        public async Task<ApiResponse<bool>> DeleteUserPermissionAsync(int permissionId)
        {
            try
            {
                var permission = await _unitOfWork.UserPermissions.GetByIdAsync(permissionId);
                if (permission == null || permission.IsDeleted)
                    return NotFound<bool>("الصلاحية غير موجودة");

                permission.IsDeleted = true;
                permission.UpdatedAt = DateTime.Now;

                await _unitOfWork.UserPermissions.UpdateAsync(permission);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("User permission deleted: {PermissionId}", permissionId);
                return Success(true, "تم حذف الصلاحية بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting user permission: {PermissionId}", permissionId);
                return BadRequest<bool>("حدث خطأ أثناء حذف الصلاحية");
            }
        }

        public async Task<ApiResponse<UserPermissionSummaryDto>> GetUserPermissionSummaryAsync(int userId)
        {
            try
            {
                var user = await _unitOfWork.Users.GetTableNoTracking()
                    .Include(u => u.UserPermissions)
                    .ThenInclude(up => up.Module)
                    .FirstOrDefaultAsync(u => u.Id == userId && !u.IsDeleted);

                if (user == null)
                    return NotFound<UserPermissionSummaryDto>("المستخدم غير موجود");

                var summary = new UserPermissionSummaryDto
                {
                    UserId = user.Id,
                    UserName = user.UserName,
                    FullName = user.FullName,
                    ModulePermissions = user.UserPermissions?
                        .Where(up => up.IsActive && !up.IsDeleted)
                        .GroupBy(up => new { up.ModuleId, up.Module?.NameEn })
                        .Select(g => new ModulePermissionDto
                        {
                            ModuleId = g.Key.ModuleId,
                            ModuleName = g.Key.NameEn ?? "",
                            Permissions = g.Select(up => up.Permission ?? "").ToList()
                        }).ToList() ?? new List<ModulePermissionDto>()
                };

                return Success(summary);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user permission summary: {UserId}", userId);
                return BadRequest<UserPermissionSummaryDto>("حدث خطأ أثناء جلب ملخص صلاحيات المستخدم");
            }
        }

        public async Task<ApiResponse<bool>> HasPermissionAsync(int userId, string moduleName, string permission)
        {
            try
            {
                var hasPermission = await _unitOfWork.UserPermissions.GetTableNoTracking()
                    .Include(up => up.Module)
                    .AnyAsync(up => up.UserId == userId &&
                                   up.Module!.NameEn == moduleName &&
                                   up.Permission == permission &&
                                   up.IsActive &&
                                   !up.IsDeleted);

                return Success(hasPermission);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking user permission: {UserId} - {ModuleName} - {Permission}",
                    userId, moduleName, permission);
                return BadRequest<bool>("حدث خطأ أثناء التحقق من الصلاحية");
            }
        }

        // Module Management Methods
        public async Task<ApiResponse<List<ModuleResponseDto>>> GetAllModulesAsync()
        {
            try
            {
                var modules = await _unitOfWork.Modules.GetTableNoTracking()
                    .Where(m => !m.IsDeleted)
                    .Include(m => m.UserPermissions)
                    .ToListAsync();

                var moduleDtos = _mapper.Map<List<ModuleResponseDto>>(modules);
                return Success(moduleDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all modules");
                return BadRequest<List<ModuleResponseDto>>("حدث خطأ أثناء جلب الوحدات");
            }
        }

        public async Task<ApiResponse<ModuleResponseDto>> GetModuleByIdAsync(int id)
        {
            try
            {
                var module = await _unitOfWork.Modules.GetTableNoTracking()
                    .Include(m => m.UserPermissions)
                    .FirstOrDefaultAsync(m => m.Id == id && !m.IsDeleted);

                if (module == null)
                    return NotFound<ModuleResponseDto>("الوحدة غير موجودة");

                var moduleDto = _mapper.Map<ModuleResponseDto>(module);
                return Success(moduleDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting module by id: {ModuleId}", id);
                return BadRequest<ModuleResponseDto>("حدث خطأ أثناء جلب الوحدة");
            }
        }

        public async Task<ApiResponse<ModuleResponseDto>> CreateModuleAsync(CreateModuleDto moduleDto)
        {
            try
            {
                var module = _mapper.Map<Module>(moduleDto);
                module.CreatedAt = DateTime.Now;

                await _unitOfWork.Modules.AddAsync(module);
                await _unitOfWork.SaveChangesAsync();

                var result = await GetModuleByIdAsync(module.Id);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating module");
                return BadRequest<ModuleResponseDto>("حدث خطأ أثناء إنشاء الوحدة");
            }
        }

        public async Task<ApiResponse<ModuleResponseDto>> UpdateModuleAsync(UpdateModuleDto moduleDto)
        {
            try
            {
                var existingModule = await _unitOfWork.Modules.GetByIdAsync(moduleDto.Id);
                if (existingModule == null || existingModule.IsDeleted)
                    return NotFound<ModuleResponseDto>("الوحدة غير موجودة");

                _mapper.Map(moduleDto, existingModule);
                existingModule.UpdatedAt = DateTime.Now;

                await _unitOfWork.Modules.UpdateAsync(existingModule);
                await _unitOfWork.SaveChangesAsync();

                var result = await GetModuleByIdAsync(existingModule.Id);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating module: {ModuleId}", moduleDto.Id);
                return BadRequest<ModuleResponseDto>("حدث خطأ أثناء تحديث الوحدة");
            }
        }

        public async Task<ApiResponse<bool>> DeleteModuleAsync(int id)
        {
            try
            {
                var module = await _unitOfWork.Modules.GetByIdAsync(id);
                if (module == null || module.IsDeleted)
                    return NotFound<bool>("الوحدة غير موجودة");

                // Check if module has active permissions
                var hasActivePermissions = await _unitOfWork.UserPermissions.GetTableNoTracking()
                    .AnyAsync(up => up.ModuleId == id && !up.IsDeleted);

                if (hasActivePermissions)
                    return BadRequest<bool>("لا يمكن حذف الوحدة لوجود صلاحيات مرتبطة بها");

                module.IsDeleted = true;
                module.UpdatedAt = DateTime.Now;

                await _unitOfWork.Modules.UpdateAsync(module);
                await _unitOfWork.SaveChangesAsync();

                return Success(true, "تم حذف الوحدة بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting module: {ModuleId}", id);
                return BadRequest<bool>("حدث خطأ أثناء حذف الوحدة");
            }
        }
    }
}
