using GamalComapany.Service.Authorization;
using GamalComapany.Service.Repositories.Interfaces;
using GamalComapany.Service.Services;
using GamalComapnyApp.API.Base;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace GamalComapnyApp.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class ImageController : AppControllerBase
    {
        private readonly IImageService _imageService;

        public ImageController(IUnitOfWork work, IImageService imageService) : base(work)
        {
            _imageService = imageService;
        }

        #region General Image Operations

        /// <summary>
        /// Upload general image
        /// </summary>
        [HttpPost("upload")]
        [RequireInventoryWrite]
        public async Task<IActionResult> UploadImage(IFormFile file, [FromForm] string folder = "general")
        {
            try
            {
                if (file == null || file.Length == 0)
                    return BadRequest("لم يتم تحديد ملف");

                if (!await _imageService.ValidateImageAsync(file))
                    return BadRequest("ملف الصورة غير صالح");

                var imagePath = await _imageService.UploadImageAsync(file, folder);
                
                return Ok(new { 
                    success = true, 
                    message = "تم رفع الصورة بنجاح", 
                    imagePath = imagePath 
                });
            }
            catch (Exception ex)
            {
                return BadRequest($"حدث خطأ أثناء رفع الصورة: {ex.Message}");
            }
        }

        /// <summary>
        /// Get image
        /// </summary>
        [HttpGet("{*imagePath}")]
        [AllowAnonymous]
        public async Task<IActionResult> GetImage(string imagePath)
        {
            try
            {
                var imageBytes = await _imageService.GetImageAsync(imagePath);
                if (imageBytes == null)
                    return NotFound("الصورة غير موجودة");

                var contentType = GetContentType(imagePath);
                return File(imageBytes, contentType);
            }
            catch (Exception ex)
            {
                return BadRequest($"حدث خطأ أثناء جلب الصورة: {ex.Message}");
            }
        }

        /// <summary>
        /// Delete image
        /// </summary>
        [HttpDelete]
        [RequireInventoryWrite]
        public async Task<IActionResult> DeleteImage([FromQuery] string imagePath)
        {
            try
            {
                var result = await _imageService.DeleteImageAsync(imagePath);
                if (result)
                {
                    return Ok(new { success = true, message = "تم حذف الصورة بنجاح" });
                }
                else
                {
                    return NotFound("الصورة غير موجودة");
                }
            }
            catch (Exception ex)
            {
                return BadRequest($"حدث خطأ أثناء حذف الصورة: {ex.Message}");
            }
        }

        /// <summary>
        /// Resize image
        /// </summary>
        [HttpPost("resize")]
        [RequireInventoryWrite]
        public async Task<IActionResult> ResizeImage([FromBody] ResizeImageRequest request)
        {
            try
            {
                if (!await _imageService.ImageExistsAsync(request.ImagePath))
                    return NotFound("الصورة غير موجودة");

                var resizedImagePath = await _imageService.ResizeImageAsync(request.ImagePath, request.Width, request.Height);
                
                return Ok(new { 
                    success = true, 
                    message = "تم تغيير حجم الصورة بنجاح", 
                    resizedImagePath = resizedImagePath 
                });
            }
            catch (Exception ex)
            {
                return BadRequest($"حدث خطأ أثناء تغيير حجم الصورة: {ex.Message}");
            }
        }

        #endregion

        #region User Profile Images

        /// <summary>
        /// Upload user profile image
        /// </summary>
        [HttpPost("profile")]
        public async Task<IActionResult> UploadProfileImage(IFormFile file)
        {
            try
            {
                if (file == null || file.Length == 0)
                    return BadRequest("لم يتم تحديد ملف");

                if (!await _imageService.ValidateImageAsync(file))
                    return BadRequest("ملف الصورة غير صالح");

                var userId = int.Parse(User.FindFirst("UserId")?.Value ?? "0");
                var imagePath = await _imageService.UploadUserProfileImageAsync(file, userId);
                
                return Ok(new { 
                    success = true, 
                    message = "تم رفع صورة الملف الشخصي بنجاح", 
                    imagePath = imagePath 
                });
            }
            catch (Exception ex)
            {
                return BadRequest($"حدث خطأ أثناء رفع صورة الملف الشخصي: {ex.Message}");
            }
        }

        /// <summary>
        /// Delete user profile image
        /// </summary>
        [HttpDelete("profile")]
        public async Task<IActionResult> DeleteProfileImage()
        {
            try
            {
                var userId = int.Parse(User.FindFirst("UserId")?.Value ?? "0");
                var result = await _imageService.DeleteUserProfileImageAsync(userId);
                
                if (result)
                {
                    return Ok(new { success = true, message = "تم حذف صورة الملف الشخصي بنجاح" });
                }
                else
                {
                    return NotFound("صورة الملف الشخصي غير موجودة");
                }
            }
            catch (Exception ex)
            {
                return BadRequest($"حدث خطأ أثناء حذف صورة الملف الشخصي: {ex.Message}");
            }
        }

        #endregion

        #region Image Information

        /// <summary>
        /// Get supported image formats
        /// </summary>
        [HttpGet("formats")]
        [AllowAnonymous]
        public async Task<IActionResult> GetSupportedFormats()
        {
            var formats = await _imageService.GetSupportedFormats();
            return Ok(new { formats = formats });
        }

        /// <summary>
        /// Get maximum file size
        /// </summary>
        [HttpGet("max-size")]
        [AllowAnonymous]
        public async Task<IActionResult> GetMaxFileSize()
        {
            var maxSize = await _imageService.GetMaxFileSizeAsync();
            return Ok(new { maxSizeBytes = maxSize, maxSizeMB = maxSize / (1024 * 1024) });
        }

        /// <summary>
        /// Check if image exists
        /// </summary>
        [HttpGet("exists")]
        public async Task<IActionResult> ImageExists([FromQuery] string imagePath)
        {
            var exists = await _imageService.ImageExistsAsync(imagePath);
            return Ok(new { exists = exists });
        }

        #endregion

        #region Maintenance

        /// <summary>
        /// Cleanup orphaned images
        /// </summary>
        [HttpPost("cleanup")]
        [RequireSystemAdmin]
        public async Task<IActionResult> CleanupOrphanedImages()
        {
            try
            {
                await _imageService.CleanupOrphanedImagesAsync();
                return Ok(new { success = true, message = "تم تنظيف الصور المهجورة بنجاح" });
            }
            catch (Exception ex)
            {
                return BadRequest($"حدث خطأ أثناء تنظيف الصور: {ex.Message}");
            }
        }

        #endregion

        #region Helper Methods

        private string GetContentType(string imagePath)
        {
            var extension = Path.GetExtension(imagePath).ToLowerInvariant();
            return extension switch
            {
                ".jpg" or ".jpeg" => "image/jpeg",
                ".png" => "image/png",
                ".webp" => "image/webp",
                ".gif" => "image/gif",
                ".bmp" => "image/bmp",
                _ => "application/octet-stream"
            };
        }

        #endregion
    }

    // Helper classes for request models
    public class ResizeImageRequest
    {
        public string ImagePath { get; set; } = string.Empty;
        public int Width { get; set; }
        public int Height { get; set; }
    }
}
