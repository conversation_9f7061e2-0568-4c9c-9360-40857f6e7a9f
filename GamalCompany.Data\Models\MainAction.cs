﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GamalCompany.Data.Models
{
    public class MainAction : BaseName
    {
        public int? ParentActionId { get; set; }
        public int? ActionTypeId { get; set; }

        [ForeignKey(nameof(ParentActionId))]
        public MainAction? ParentAction { get; set; }

        public ICollection<MainAction>? ChildActions { get; set; }

        [ForeignKey(nameof(ActionTypeId))]
        public ActionType ActionTypes { get; set; } = null!;
    }
}
