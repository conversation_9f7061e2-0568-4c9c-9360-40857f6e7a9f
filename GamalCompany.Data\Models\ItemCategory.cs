﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GamalCompany.Data.Models
{
    public class ItemCategory : BaseName
    {
        public int?  ParentCategoryId { get; set; }
        public string? Code { get; set; }
        public string? Symbol { get; set; }
        public int? CategoryTypeId {  get; set; } //item,Product,RawMaterial,Component,FinishedProduct,Packaging
        public string? ImageUrl {  get; set; }
        public int? SortOrder { get; set; }

        [ForeignKey(nameof(ParentCategoryId))]
        public ItemCategory? ParentCategory { get; set; }
        public ICollection<ItemCategory>? ChildCategories { get; set; }
    
        [ForeignKey(nameof(CategoryTypeId))]
        public virtual CategoryType CategoryTypes { get; set; } = null!;
    }
}
