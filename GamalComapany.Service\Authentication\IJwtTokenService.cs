using GamalComapany.Service.Dtos.UserDto;
using GamalCompany.Data.Models;
using System.Security.Claims;

namespace GamalComapany.Service.Authentication
{
    public interface IJwtTokenService
    {
        Task<string> GenerateAccessTokenAsync(User user, List<string> permissions);
        Task<string> GenerateRefreshTokenAsync();
        Task<ClaimsPrincipal?> ValidateTokenAsync(string token);
        Task<bool> ValidateRefreshTokenAsync(string refreshToken, int userId);
        Task<string> RefreshAccessTokenAsync(string refreshToken);
        Task RevokeRefreshTokenAsync(string refreshToken);
        Task RevokeAllUserTokensAsync(int userId);
        Task CleanupExpiredTokensAsync();
        int GetUserIdFromToken(string token);
        List<string> GetPermissionsFromToken(string token);
    }
}
