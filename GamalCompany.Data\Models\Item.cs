﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GamalCompany.Data.Models
{
    public class Item : BaseName
    {
        public string? NameAr { get; set; }
        public string? ItemCode { get; set; }
        public int CategoryId { get; set; }
        public string? Barcode { get; set; }
        public int UnitId { get; set; }

        [Column(TypeName = "decimal(18,4)")]
        public decimal StandardCost { get; set; }//التكلفة القياسية

        [Column(TypeName = "decimal(18,4)")]
        public decimal? MinimumStock { get; set; }//الحد الأدنى للمخزون

        [Column(TypeName = "decimal(18,4)")]
        public decimal? MaximumStock { get; set; }// الحد الأقصى للمخزون

        [Column(TypeName = "decimal(18,4)")]
        public decimal? ReorderLevel { get; set; }//إعادة ترتيب المستوى
        public int? SortOrder { get; set; } = 1;
        public int? ItemType { get; set; } = 1; //item //Product


        // Navigation Properties

        [ForeignKey(nameof(CategoryId))]
        public virtual ItemCategory ItemCategory { get; set; } = null!;

        [ForeignKey(nameof(UnitId))]
        public virtual Unit ItemUnit { get; set; } = null!;

        public virtual ICollection<ItemImage>? ItemImages { get; set; }
        public virtual ICollection<InvoiceDetail>? InvoiceDetails { get; set; }
        public virtual ICollection<InventoryTransaction>? InventoryTransactions { get; set; }

    }
}
