using Microsoft.AspNetCore.Http;
using System.Security.Claims;

namespace GamalComapny.Service.Context
{
    public class UserContext : IUserContext
    {
        private readonly IHttpContextAccessor _httpContextAccessor;

        public UserContext(IHttpContextAccessor httpContextAccessor)
        {
            _httpContextAccessor = httpContextAccessor;
        }

        public int? UserId
        {
            get
            {
                var userIdClaim = _httpContextAccessor.HttpContext?.User?.FindFirst("UserId")?.Value;
                return int.TryParse(userIdClaim, out var userId) ? userId : null;
            }
        }

        public string? UserName => _httpContextAccessor.HttpContext?.User?.FindFirst(ClaimTypes.Name)?.Value;

        public string? FullName => _httpContextAccessor.HttpContext?.User?.FindFirst("FullName")?.Value;

        public List<string> Permissions
        {
            get
            {
                return _httpContextAccessor.HttpContext?.User?.Claims
                    .Where(c => c.Type == "permission")
                    .Select(c => c.Value)
                    .ToList() ?? new List<string>();
            }
        }

        public bool IsAuthenticated => _httpContextAccessor.HttpContext?.User?.Identity?.IsAuthenticated ?? false;

        public bool HasPermission(string module, string permission)
        {
            var requiredPermission = $"{module}:{permission}";
            return Permissions.Contains(requiredPermission);
        }

        public string? IpAddress
        {
            get
            {
                var context = _httpContextAccessor.HttpContext;
                if (context == null) return null;

                // Check for forwarded IP first (in case of proxy/load balancer)
                var forwardedFor = context.Request.Headers["X-Forwarded-For"].FirstOrDefault();
                if (!string.IsNullOrEmpty(forwardedFor))
                {
                    return forwardedFor.Split(',')[0].Trim();
                }

                // Check for real IP header
                var realIp = context.Request.Headers["X-Real-IP"].FirstOrDefault();
                if (!string.IsNullOrEmpty(realIp))
                {
                    return realIp;
                }

                // Fall back to connection remote IP
                return context.Connection.RemoteIpAddress?.ToString();
            }
        }

        public string? UserAgent => _httpContextAccessor.HttpContext?.Request?.Headers["User-Agent"].FirstOrDefault();
    }
}
