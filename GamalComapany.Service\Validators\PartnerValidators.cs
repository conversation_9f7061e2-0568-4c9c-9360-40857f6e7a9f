using FluentValidation;
using GamalComapany.Service.Dtos.PartnerDto;

namespace GamalComapany.Service.Validators
{
    public class CreatePartnerDtoValidator : AbstractValidator<CreatePartnerDto>
    {
        public CreatePartnerDtoValidator()
        {
            RuleFor(x => x.NameEn)
                .NotEmpty().WithMessage("اسم الشريك مطلوب")
                .MaximumLength(100).WithMessage("اسم الشريك يجب أن يكون أقل من 100 حرف");

            RuleFor(x => x.Description)
                .MaximumLength(500).WithMessage("الوصف يجب أن يكون أقل من 500 حرف");

            RuleFor(x => x.InitialInvestment)
                .GreaterThan(0).WithMessage("الاستثمار الأولي يجب أن يكون أكبر من صفر");

            RuleFor(x => x.SharePercentage)
                .GreaterThan(0).WithMessage("نسبة المشاركة يجب أن تكون أكبر من صفر")
                .LessThanOrEqualTo(100).WithMessage("نسبة المشاركة يجب أن تكون أقل من أو تساوي 100")
                .When(x => x.SharePercentage.HasValue);
        }
    }

    public class UpdatePartnerDtoValidator : AbstractValidator<UpdatePartnerDto>
    {
        public UpdatePartnerDtoValidator()
        {
            RuleFor(x => x.Id)
                .GreaterThan(0).WithMessage("معرف الشريك مطلوب");

            RuleFor(x => x.NameEn)
                .NotEmpty().WithMessage("اسم الشريك مطلوب")
                .MaximumLength(100).WithMessage("اسم الشريك يجب أن يكون أقل من 100 حرف");

            RuleFor(x => x.Description)
                .MaximumLength(500).WithMessage("الوصف يجب أن يكون أقل من 500 حرف");

            RuleFor(x => x.InitialInvestment)
                .GreaterThan(0).WithMessage("الاستثمار الأولي يجب أن يكون أكبر من صفر");

            RuleFor(x => x.SharePercentage)
                .GreaterThan(0).WithMessage("نسبة المشاركة يجب أن تكون أكبر من صفر")
                .LessThanOrEqualTo(100).WithMessage("نسبة المشاركة يجب أن تكون أقل من أو تساوي 100")
                .When(x => x.SharePercentage.HasValue);
        }
    }

    public class PartnerTransactionDtoValidator : AbstractValidator<PartnerTransactionDto>
    {
        public PartnerTransactionDtoValidator()
        {
            RuleFor(x => x.PartnerId)
                .GreaterThan(0).WithMessage("معرف الشريك مطلوب");

            RuleFor(x => x.TransactionDate)
                .NotEmpty().WithMessage("تاريخ المعاملة مطلوب")
                .LessThanOrEqualTo(DateTime.Now).WithMessage("تاريخ المعاملة لا يمكن أن يكون في المستقبل");

            RuleFor(x => x.ActionDetailId)
                .GreaterThan(0).WithMessage("نوع المعاملة مطلوب");

            RuleFor(x => x.Amount)
                .GreaterThan(0).WithMessage("المبلغ يجب أن يكون أكبر من صفر");

            RuleFor(x => x.Description)
                .MaximumLength(500).WithMessage("الوصف يجب أن يكون أقل من 500 حرف");

            RuleFor(x => x.Notes)
                .MaximumLength(1000).WithMessage("الملاحظات يجب أن تكون أقل من 1000 حرف");
        }
    }
}
