using AutoMapper;
using GamalComapany.Service.Dtos;
using GamalComapany.Service.Dtos.PartnerDto;
using GamalComapany.Service.Repositories.Interfaces;
using GamalCompany.Data.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace GamalComapany.Service.Repositories.Implementations
{
    public class PartnerService : ResponseH<PERSON>ler, IPartnerRepository
    {
        private readonly IUnitOfWorkOfService _unitOfWork;
        private readonly IMapper _mapper;
        private readonly ILogger<PartnerService> _logger;

        public PartnerService(IUnitOfWorkOfService unitOfWork, IMapper mapper, ILogger<PartnerService> logger)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
            _logger = logger;
        }

        public async Task<ApiResponse<List<PartnerResponseDto>>> GetAllPartnersAsync()
        {
            try
            {
                var partners = await _unitOfWork.Partners.GetTableNoTracking()
                    .Where(p => !p.IsDeleted)
                    .Include(p => p.PartnerTransations)
                    .ToListAsync();

                if (!partners.Any())
                    return NotFound<List<PartnerResponseDto>>("لا توجد شركاء");

                var partnerDtos = new List<PartnerResponseDto>();
                foreach (var partner in partners)
                {
                    var partnerDto = _mapper.Map<PartnerResponseDto>(partner);
                    
                    // Calculate financial data
                    var transactions = partner.PartnerTransations.Where(t => !t.IsDeleted).ToList();
                    partnerDto.TotalInvestments = transactions.Where(t => t.ActionDetailId == 1).Sum(t => t.Amount); // Investment
                    partnerDto.TotalWithdrawals = transactions.Where(t => t.ActionDetailId == 2).Sum(t => t.Amount); // Withdrawal
                    partnerDto.CurrentCapital = (partner.InitialInvestment ?? 0) + partnerDto.TotalInvestments - partnerDto.TotalWithdrawals;
                    
                    partnerDto.Transactions = _mapper.Map<List<PartnerTransactionResponseDto>>(transactions.OrderByDescending(t => t.TransactionDate));
                    
                    partnerDtos.Add(partnerDto);
                }

                return Success(partnerDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all partners");
                return BadRequest<List<PartnerResponseDto>>("حدث خطأ أثناء جلب بيانات الشركاء");
            }
        }

        public async Task<ApiResponse<PartnerResponseDto>> GetPartnerByIdAsync(int id)
        {
            try
            {
                var partner = await _unitOfWork.Partners.GetTableNoTracking()
                    .Include(p => p.PartnerTransations)
                    .FirstOrDefaultAsync(p => p.Id == id && !p.IsDeleted);

                if (partner == null)
                    return NotFound<PartnerResponseDto>("الشريك غير موجود");

                var partnerDto = _mapper.Map<PartnerResponseDto>(partner);
                
                // Calculate financial data
                var transactions = partner.PartnerTransations.Where(t => !t.IsDeleted).ToList();
                partnerDto.TotalInvestments = transactions.Where(t => t.ActionDetailId == 1).Sum(t => t.Amount);
                partnerDto.TotalWithdrawals = transactions.Where(t => t.ActionDetailId == 2).Sum(t => t.Amount);
                partnerDto.CurrentCapital = (partner.InitialInvestment ?? 0) + partnerDto.TotalInvestments - partnerDto.TotalWithdrawals;
                
                partnerDto.Transactions = _mapper.Map<List<PartnerTransactionResponseDto>>(transactions.OrderByDescending(t => t.TransactionDate));

                return Success(partnerDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting partner by id: {PartnerId}", id);
                return BadRequest<PartnerResponseDto>("حدث خطأ أثناء جلب بيانات الشريك");
            }
        }

        public async Task<ApiResponse<PartnerResponseDto>> CreatePartnerAsync(CreatePartnerDto createPartnerDto)
        {
            try
            {
                // Validate total share percentages
                var existingPartners = await _unitOfWork.Partners.GetTableNoTracking()
                    .Where(p => !p.IsDeleted)
                    .ToListAsync();

                var totalExistingShares = existingPartners.Sum(p => p.SharePercentage ?? 0);
                var newSharePercentage = createPartnerDto.SharePercentage ?? 0;

                if (totalExistingShares + newSharePercentage > 100)
                {
                    return BadRequest<PartnerResponseDto>("مجموع نسب المشاركة لا يمكن أن يتجاوز 100%");
                }

                var partner = _mapper.Map<Partner>(createPartnerDto);
                partner.CreatedAt = DateTime.Now;

                await _unitOfWork.Partners.AddAsync(partner);
                await _unitOfWork.SaveChangesAsync();

                var result = await GetPartnerByIdAsync(partner.Id);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating partner");
                return BadRequest<PartnerResponseDto>("حدث خطأ أثناء إنشاء الشريك");
            }
        }

        public async Task<ApiResponse<PartnerResponseDto>> UpdatePartnerAsync(UpdatePartnerDto updatePartnerDto)
        {
            try
            {
                var existingPartner = await _unitOfWork.Partners.GetByIdAsync(updatePartnerDto.Id);
                if (existingPartner == null || existingPartner.IsDeleted)
                    return NotFound<PartnerResponseDto>("الشريك غير موجود");

                // Validate share percentages
                var otherPartners = await _unitOfWork.Partners.GetTableNoTracking()
                    .Where(p => !p.IsDeleted && p.Id != updatePartnerDto.Id)
                    .ToListAsync();

                var totalOtherShares = otherPartners.Sum(p => p.SharePercentage ?? 0);
                var newSharePercentage = updatePartnerDto.SharePercentage ?? 0;

                if (totalOtherShares + newSharePercentage > 100)
                {
                    return BadRequest<PartnerResponseDto>("مجموع نسب المشاركة لا يمكن أن يتجاوز 100%");
                }

                _mapper.Map(updatePartnerDto, existingPartner);
                existingPartner.UpdatedAt = DateTime.Now;

                await _unitOfWork.Partners.UpdateAsync(existingPartner);
                await _unitOfWork.SaveChangesAsync();

                var result = await GetPartnerByIdAsync(existingPartner.Id);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating partner: {PartnerId}", updatePartnerDto.Id);
                return BadRequest<PartnerResponseDto>("حدث خطأ أثناء تحديث الشريك");
            }
        }

        public async Task<ApiResponse<bool>> DeletePartnerAsync(int id)
        {
            try
            {
                var partner = await _unitOfWork.Partners.GetByIdAsync(id);
                if (partner == null || partner.IsDeleted)
                    return NotFound<bool>("الشريك غير موجود");

                // Check if partner has transactions
                var hasTransactions = await _unitOfWork.PartnerTransations.GetTableNoTracking()
                    .AnyAsync(t => t.PartnerId == id && !t.IsDeleted);

                if (hasTransactions)
                {
                    return BadRequest<bool>("لا يمكن حذف الشريك لوجود معاملات مرتبطة به");
                }

                partner.IsDeleted = true;
                partner.UpdatedAt = DateTime.Now;

                await _unitOfWork.Partners.UpdateAsync(partner);
                await _unitOfWork.SaveChangesAsync();

                return Success(true, "تم حذف الشريك بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting partner: {PartnerId}", id);
                return BadRequest<bool>("حدث خطأ أثناء حذف الشريك");
            }
        }

        public async Task<ApiResponse<List<PartnerCapitalCalculationDto>>> CalculatePartnersCapitalAsync()
        {
            try
            {
                var partners = await _unitOfWork.Partners.GetTableNoTracking()
                    .Where(p => !p.IsDeleted)
                    .Include(p => p.PartnerTransations)
                    .ToListAsync();

                var calculations = new List<PartnerCapitalCalculationDto>();
                decimal totalCapital = 0;

                foreach (var partner in partners)
                {
                    var transactions = partner.PartnerTransations.Where(t => !t.IsDeleted).ToList();
                    var totalInvestments = transactions.Where(t => t.ActionDetailId == 1).Sum(t => t.Amount);
                    var totalWithdrawals = transactions.Where(t => t.ActionDetailId == 2).Sum(t => t.Amount);
                    var currentCapital = (partner.InitialInvestment ?? 0) + totalInvestments - totalWithdrawals;

                    totalCapital += currentCapital;

                    calculations.Add(new PartnerCapitalCalculationDto
                    {
                        PartnerId = partner.Id,
                        PartnerName = partner.NameEn,
                        InitialInvestment = partner.InitialInvestment ?? 0,
                        TotalAdditionalInvestments = totalInvestments,
                        TotalWithdrawals = totalWithdrawals,
                        CurrentCapital = currentCapital,
                        SharePercentage = partner.SharePercentage ?? 0
                    });
                }

                // Calculate actual share percentages
                foreach (var calc in calculations)
                {
                    calc.CalculatedSharePercentage = totalCapital > 0 ? (calc.CurrentCapital / totalCapital) * 100 : 0;
                    calc.RequiresAdjustment = Math.Abs(calc.SharePercentage - calc.CalculatedSharePercentage) > 0.01m;
                }

                return Success(calculations);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating partners capital");
                return BadRequest<List<PartnerCapitalCalculationDto>>("حدث خطأ أثناء حساب رؤوس أموال الشركاء");
            }
        }

        public async Task<ApiResponse<bool>> RecalculateSharePercentagesAsync()
        {
            try
            {
                var calculations = await CalculatePartnersCapitalAsync();
                if (!calculations.Succeeded)
                    return BadRequest<bool>("فشل في حساب رؤوس الأموال");

                await _unitOfWork.BeginTransactionAsync();

                foreach (var calc in calculations.Data)
                {
                    var partner = await _unitOfWork.Partners.GetByIdAsync(calc.PartnerId);
                    if (partner != null)
                    {
                        partner.SharePercentage = calc.CalculatedSharePercentage;
                        partner.UpdatedAt = DateTime.Now;
                        await _unitOfWork.Partners.UpdateAsync(partner);
                    }
                }

                await _unitOfWork.SaveChangesAsync();
                await _unitOfWork.CommitTransactionAsync();

                return Success(true, "تم إعادة حساب نسب المشاركة بنجاح");
            }
            catch (Exception ex)
            {
                await _unitOfWork.RollbackTransactionAsync();
                _logger.LogError(ex, "Error recalculating share percentages");
                return BadRequest<bool>("حدث خطأ أثناء إعادة حساب نسب المشاركة");
            }
        }

        public async Task<ApiResponse<List<PartnerProfitDistributionDto>>> CalculateProfitDistributionAsync(decimal totalProfit)
        {
            try
            {
                var partners = await _unitOfWork.Partners.GetTableNoTracking()
                    .Where(p => !p.IsDeleted && p.IsActive)
                    .ToListAsync();

                var distributions = partners.Select(p => new PartnerProfitDistributionDto
                {
                    PartnerId = p.Id,
                    PartnerName = p.NameEn,
                    SharePercentage = p.SharePercentage ?? 0,
                    ProfitShare = totalProfit * ((p.SharePercentage ?? 0) / 100),
                    TotalProfit = totalProfit,
                    CalculationDate = DateTime.Now
                }).ToList();

                return Success(distributions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating profit distribution");
                return BadRequest<List<PartnerProfitDistributionDto>>("حدث خطأ أثناء حساب توزيع الأرباح");
            }
        }

        public async Task<ApiResponse<List<PartnerTransactionResponseDto>>> GetPartnerTransactionsAsync(int partnerId)
        {
            try
            {
                var transactions = await _unitOfWork.PartnerTransations.GetTableNoTracking()
                    .Where(t => t.PartnerId == partnerId && !t.IsDeleted)
                    .OrderByDescending(t => t.TransactionDate)
                    .ToListAsync();

                var transactionDtos = _mapper.Map<List<PartnerTransactionResponseDto>>(transactions);
                return Success(transactionDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting partner transactions: {PartnerId}", partnerId);
                return BadRequest<List<PartnerTransactionResponseDto>>("حدث خطأ أثناء جلب معاملات الشريك");
            }
        }

        public async Task<ApiResponse<PartnerTransactionResponseDto>> CreatePartnerTransactionAsync(PartnerTransactionDto transactionDto)
        {
            try
            {
                var partner = await _unitOfWork.Partners.GetByIdAsync(transactionDto.PartnerId);
                if (partner == null || partner.IsDeleted)
                    return NotFound<PartnerTransactionResponseDto>("الشريك غير موجود");

                var transaction = _mapper.Map<PartnerTransation>(transactionDto);
                transaction.CreatedAt = DateTime.Now;

                await _unitOfWork.PartnerTransations.AddAsync(transaction);
                await _unitOfWork.SaveChangesAsync();

                var result = _mapper.Map<PartnerTransactionResponseDto>(transaction);
                return Success(result, "تم إنشاء المعاملة بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating partner transaction");
                return BadRequest<PartnerTransactionResponseDto>("حدث خطأ أثناء إنشاء المعاملة");
            }
        }

        public async Task<ApiResponse<bool>> DeletePartnerTransactionAsync(int transactionId)
        {
            try
            {
                var transaction = await _unitOfWork.PartnerTransations.GetByIdAsync(transactionId);
                if (transaction == null || transaction.IsDeleted)
                    return NotFound<bool>("المعاملة غير موجودة");

                transaction.IsDeleted = true;
                transaction.UpdatedAt = DateTime.Now;

                await _unitOfWork.PartnerTransations.UpdateAsync(transaction);
                await _unitOfWork.SaveChangesAsync();

                return Success(true, "تم حذف المعاملة بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting partner transaction: {TransactionId}", transactionId);
                return BadRequest<bool>("حدث خطأ أثناء حذف المعاملة");
            }
        }
    }
}
